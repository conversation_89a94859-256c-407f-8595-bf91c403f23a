import dynamic from 'next/dynamic';
import { ReactNode } from 'react';

interface NoSSRWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

// This component will only render on the client side
const NoSSRComponent = ({ children }: { children: ReactNode }) => {
  return <>{children}</>;
};

// Create a dynamic component that disables SSR
const NoSSRWrapper = dynamic(
  () => Promise.resolve(NoSSRComponent),
  {
    ssr: false,
    loading: () => <div>Loading...</div>
  }
);

export default function NoSSR({ children, fallback }: NoSSRWrapperProps) {
  return (
    <NoSSRWrapper>
      {children}
    </NoSSRWrapper>
  );
}
