import type { NextConfig } from "next";
import { createMDX } from 'fumadocs-mdx/next';

const nextConfig: NextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable SSR by using static export
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
};

// Wrap the config with createMDX
const withMDX = createMDX({});

export default withMDX(nextConfig);
