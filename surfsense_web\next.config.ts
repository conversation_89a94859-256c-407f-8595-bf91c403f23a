import type { NextConfig } from "next";
import { createMDX } from 'fumadocs-mdx/next';

const nextConfig: NextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable SSR globally for the entire application
  experimental: {
    ssr: false,
  },
};

// Wrap the config with createMDX
const withMDX = createMDX({});

export default withMDX(nextConfig);
